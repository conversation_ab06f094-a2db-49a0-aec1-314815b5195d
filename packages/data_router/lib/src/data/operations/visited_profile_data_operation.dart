import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data_router.dart';
import 'base_data_operation.dart';

/// VisitedProfile-specific data operation implementation
/// Provides reactive data access with automatic synchronization for VisitedProfile entities
///
/// This follows the Single Responsibility Principle by handling only VisitedProfile entities
/// and implements the local-first strategy with entity-specific optimizations
@LazySingleton()
class VisitedProfileDataOperation extends BaseDataOperation<VisitedProfile> {
  final VisitedProfileLocalDataSource localSource;
  final VisitedProfileRemoteDataSource remoteSource;
  final VisitedProfileWebSocketDataSource webSocketSource;

  VisitedProfileDataOperation(
    this.localSource,
    this.remoteSource,
    this.webSocketSource,
  ) : super(
          localSource: localSource,
          remoteSource: remoteSource,
          webSocketSource: webSocketSource,
        );

  //region WebSocket Event Handling

  @override
  Future<void> handleRealtimeEvent(CloudEvent event) async {
    try {
      logOperation('Handling realtime event', event.type.value);

      switch (event.type.value) {
        case 'VISITED_PROFILE_CREATED':
          await _handleVisitedProfileCreated(event);
          break;
        case 'VISITED_PROFILE_UPDATED':
          await _handleVisitedProfileUpdated(event);
          break;
        case 'VISITED_PROFILE_DELETED':
          await _handleVisitedProfileDeleted(event);
          break;
        case 'VISITED_PROFILE_CLEARED':
          await _handleVisitedProfileCleared(event);
          break;
        default:
          logOperation(
              'Unhandled visited profile event type', event.type.value);
      }
    } catch (e) {
      logOperation('Error handling realtime event', '${event.type.value}: $e');
    }
  }

  /// Handle visited profile created event
  Future<void> _handleVisitedProfileCreated(CloudEvent event) async {
    try {
      logOperation('Processing visited profile created event');

      final data = event.data as Map<String, dynamic>?;
      if (data == null) {
        logOperation('❌ No data in visited profile created event');
        return;
      }

      // Parse visited profile from event data
      final visitedProfile = VisitedProfile.create(
        sessionKey: data['sessionKey'] ?? '',
        visitedUserId: data['visitedUserId'] ?? '',
        isRead: data['isRead'] ?? false,
        createTime: data['createTime'] != null
            ? DateTime.parse(data['createTime'])
            : DateTime.now(),
        updateTime: data['updateTime'] != null
            ? DateTime.parse(data['updateTime'])
            : DateTime.now(),
      );

      await localSource.insert(visitedProfile);
      logOperation('✅ Visited profile created via WebSocket',
          visitedProfile.visitedUserId);
    } catch (e) {
      logOperation(
          '❌ Error handling visited profile created event', e.toString());
    }
  }

  /// Handle visited profile updated event
  Future<void> _handleVisitedProfileUpdated(CloudEvent event) async {
    try {
      logOperation('Processing visited profile updated event');

      final data = event.data as Map<String, dynamic>?;
      if (data == null) {
        logOperation('❌ No data in visited profile updated event');
        return;
      }

      final visitedUserId = data['visitedUserId'] as String?;
      if (visitedUserId == null) {
        logOperation('❌ No visitedUserId in visited profile updated event');
        return;
      }

      // Get existing visited profile
      final existing = await localSource.getByVisitedUserId(visitedUserId);
      if (existing == null) {
        logOperation('❌ Visited profile not found for update', visitedUserId);
        return;
      }

      // Update fields
      final updated = existing.copyWith(
        isRead: data['isRead'] ?? existing.isRead,
        updateTime: data['updateTime'] != null
            ? DateTime.parse(data['updateTime'])
            : DateTime.now(),
      );

      await localSource.insert(updated);
      logOperation('✅ Visited profile updated via WebSocket', visitedUserId);
    } catch (e) {
      logOperation(
          '❌ Error handling visited profile updated event', e.toString());
    }
  }

  /// Handle visited profile deleted event
  Future<void> _handleVisitedProfileDeleted(CloudEvent event) async {
    try {
      logOperation('Processing visited profile deleted event');

      final data = event.data as Map<String, dynamic>?;
      if (data == null) {
        logOperation('❌ No data in visited profile deleted event');
        return;
      }

      final visitedUserId = data['visitedUserId'] as String?;
      if (visitedUserId == null) {
        logOperation('❌ No visitedUserId in visited profile deleted event');
        return;
      }

      await localSource.deleteByVisitedUserId(visitedUserId);
      logOperation('✅ Visited profile deleted via WebSocket', visitedUserId);
    } catch (e) {
      logOperation(
          '❌ Error handling visited profile deleted event', e.toString());
    }
  }

  /// Handle visited profile cleared event (all marked as read)
  Future<void> _handleVisitedProfileCleared(CloudEvent event) async {
    try {
      logOperation('Processing visited profile cleared event');

      await localSource.markAllAsRead();
      logOperation('✅ All visited profiles marked as read via WebSocket');
    } catch (e) {
      logOperation(
          '❌ Error handling visited profile cleared event', e.toString());
    }
  }

  //endregion

  //region Local Data Source Operations

  /// Get visited profile by visited user ID
  /// Returns visited profile for the current session
  Future<VisitedProfile?> getByVisitedUserId(String visitedUserId) async {
    try {
      logOperation('Getting visited profile by visited user ID', visitedUserId);
      return await localSource.getByVisitedUserId(visitedUserId);
    } catch (e) {
      logOperation('Error getting visited profile by visited user ID',
          '$visitedUserId: $e');
      return null;
    }
  }

  /// Get all visited profiles for current session
  /// Returns list of visited profiles ordered by update time
  Future<List<VisitedProfile>> getAllForCurrentSession() async {
    try {
      logOperation('Getting all visited profiles for current session');
      return await localSource.getAllForCurrentSession();
    } catch (e) {
      logOperation(
          'Error getting visited profiles for current session', e.toString());
      return [];
    }
  }

  /// Watch visited profiles for current session
  /// Returns stream of visited profiles updates
  Stream<List<VisitedProfile>> watchVisitedProfiles() {
    logOperation('Watching visited profiles for current session');
    return localSource.watchVisitedProfiles();
  }

  /// Mark visited profile as read
  /// Updates the isRead flag and updateTime
  Future<Resource<bool>> markAsRead(String visitedUserId) async {
    try {
      logOperation('Marking visited profile as read', visitedUserId);

      final existing = await localSource.getByVisitedUserId(visitedUserId);
      if (existing == null) {
        logOperation(
            '❌ Visited profile not found for marking as read', visitedUserId);
        return Resource.error('Visited profile not found');
      }

      existing.markAsRead();
      await localSource.insert(existing);

      logOperation('✅ Visited profile marked as read', visitedUserId);
      return Resource.success(true);
    } catch (e) {
      logOperation(
          '❌ Error marking visited profile as read', '$visitedUserId: $e');
      return Resource.error('Failed to mark as read: $e');
    }
  }

  /// Mark all visited profiles as read
  /// Updates all visited profiles for current session
  Future<Resource<int>> markAllAsRead() async {
    try {
      logOperation('Marking all visited profiles as read');

      final count = await localSource.markAllAsRead();
      logOperation('✅ Marked $count visited profiles as read');

      return Resource.success(count);
    } catch (e) {
      logOperation(
          '❌ Error marking all visited profiles as read', e.toString());
      return Resource.error('Failed to mark all as read: $e');
    }
  }

  //endregion

  //region Remote Data Source Operations

  /// Load visited profiles from remote API
  /// Fetches visited profiles from server and saves to local storage
  Future<Resource<List<VisitedProfile>>> loadFromRemote() async {
    try {
      logOperation('Loading visited profiles from remote');

      final remoteProfiles = await remoteSource.loadAll();

      if (remoteProfiles.isNotEmpty) {
        await localSource.insertAll(remoteProfiles);
        logOperation(
            '✅ Loaded and saved ${remoteProfiles.length} visited profiles from remote');
      }

      return Resource.success(remoteProfiles);
    } catch (e) {
      logOperation(
          '❌ Error loading visited profiles from remote', e.toString());
      return Resource.error('Failed to load from remote: $e');
    }
  }

  /// Clear visited profile notifications on server
  /// Marks all visited profiles as read on server and locally
  Future<Resource<bool>> clearNotifications() async {
    try {
      logOperation('Clearing visited profile notifications');

      // Clear on server first
      final serverResult = await remoteSource.clearNotifications();
      if (!serverResult) {
        logOperation('❌ Failed to clear notifications on server');
        return Resource.error('Failed to clear notifications on server');
      }

      // Mark all as read locally
      await localSource.markAllAsRead();

      logOperation('✅ Cleared visited profile notifications');
      return Resource.success(true);
    } catch (e) {
      logOperation(
          '❌ Error clearing visited profile notifications', e.toString());
      return Resource.error('Failed to clear notifications: $e');
    }
  }

  /// Delete visited profile by visited user ID
  /// Removes visited profile locally and optionally on server
  Future<Resource<bool>> deleteByVisitedUserId(String visitedUserId) async {
    try {
      logOperation('Deleting visited profile', visitedUserId);

      // Delete locally first (optimistic update)
      await localSource.deleteByVisitedUserId(visitedUserId);

      // Try to delete on server (best effort)
      try {
        await remoteSource.delete(visitedUserId);
        logOperation('✅ Deleted visited profile on server', visitedUserId);
      } catch (e) {
        logOperation('⚠️ Failed to delete visited profile on server',
            '$visitedUserId: $e');
        // Continue - local deletion is more important
      }

      logOperation('✅ Deleted visited profile', visitedUserId);
      return Resource.success(true);
    } catch (e) {
      logOperation('❌ Error deleting visited profile', '$visitedUserId: $e');
      return Resource.error('Failed to delete visited profile: $e');
    }
  }

  /// Create or update visited profile
  /// Records a visit to a user profile
  Future<Resource<VisitedProfile>> recordVisit(String visitedUserId) async {
    try {
      logOperation('Recording visit to user profile', visitedUserId);

      // Get current session key
      final sessionKey = Config.getInstance().activeSessionKey;
      if (sessionKey == null || sessionKey.isEmpty) {
        return Resource.error('No active session');
      }

      // Check if visited profile already exists
      final existing = await localSource.getByVisitedUserId(visitedUserId);

      VisitedProfile visitedProfile;
      if (existing != null) {
        // Update existing visit
        existing.updateVisitTime();
        existing.markAsUnread(); // New visit should be unread
        visitedProfile = existing;
      } else {
        // Create new visited profile
        visitedProfile = VisitedProfile.create(
          sessionKey: sessionKey,
          visitedUserId: visitedUserId,
          isRead: false,
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
        );
      }

      // Save locally
      await localSource.insert(visitedProfile);

      // Try to record on server (best effort)
      try {
        await remoteSource.recordVisit(visitedUserId);
        logOperation('✅ Recorded visit on server', visitedUserId);
      } catch (e) {
        logOperation(
            '⚠️ Failed to record visit on server', '$visitedUserId: $e');
        // Continue - local recording is more important
      }

      logOperation('✅ Recorded visit to user profile', visitedUserId);
      return Resource.success(visitedProfile);
    } catch (e) {
      logOperation('❌ Error recording visit', '$visitedUserId: $e');
      return Resource.error('Failed to record visit: $e');
    }
  }

  /// Delete all visited profiles by session key
  /// This is a VisitedProfile-specific operation for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting visited profiles by session key', sessionKey);
      await localSource.deleteAllBySessionKey(sessionKey);
    } catch (e) {
      logOperation(
        'Error deleting visited profiles by session key',
        '$sessionKey: $e',
      );
    }
  }

  //endregion
}
