import 'package:injectable/injectable.dart';

import '../../../../data_router.dart';
import 'base_remote_data_source.dart';

/// VisitedProfile-specific remote data source implementation
/// Handles VisitedProfile entity operations using VisitedProfileService interface
///
/// This follows the Single Responsibility Principle by handling only VisitedProfile entities
/// and delegates actual API operations to the injected VisitedProfileService.
///
/// This mirrors the pattern used by VisitedProfileLocalDataSource which delegates to VisitedProfileRepository.
@LazySingleton()
class VisitedProfileRemoteDataSource
    extends BaseRemoteDataSource<VisitedProfile> {
  final VisitedProfileService _visitedProfileService;

  VisitedProfileRemoteDataSource(this._visitedProfileService);

  @override
  EntityEnum get entity => EntityEnum.visitedProfile;

  @override
  Future<VisitedProfile?> load(String id) async {
    try {
      logOperation('Fetching visited profile from API', id);

      final visitedProfile = await _visitedProfileService.getVisitedProfile(id);

      if (visitedProfile != null) {
        logOperation('Fetched visited profile successfully', id);
        return visitedProfile;
      } else {
        logOperation('Visited profile not found on remote', id);
        return null;
      }
    } catch (e) {
      logOperation('Error fetching visited profile', '$id: $e');
      handleError('fetch', e);
      return null;
    }
  }

  @override
  Future<List<VisitedProfile>> loadAll() async {
    try {
      logOperation('Fetching all visited profiles from API');

      final visitedProfiles = await _visitedProfileService.getVisitedProfiles();
      logOperation(
        'Fetched visited profiles from remote API',
        '${visitedProfiles.length} profiles',
      );
      return visitedProfiles;
    } catch (e) {
      logOperation('Error fetching all visited profiles', e.toString());
      handleError('fetchAll', e);
      return [];
    }
  }

  /// Record a visit to a user profile on remote server
  /// This is a specialized method for visited profile entities
  Future<bool> recordVisit(String visitedUserId) async {
    try {
      logOperation('Recording visit to user profile on API', visitedUserId);

      final success = await _visitedProfileService.recordVisit(visitedUserId);

      if (success) {
        logOperation('Recorded visit successfully on remote', visitedUserId);
        return true;
      } else {
        logOperation('Failed to record visit on remote', visitedUserId);
        return false;
      }
    } catch (e) {
      logOperation('Error recording visit on remote', '$visitedUserId: $e');
      handleError('recordVisit', e);
      return false;
    }
  }

  /// Clear visited profile notifications on remote server
  /// Marks all visited profiles as read on server
  Future<bool> clearNotifications() async {
    try {
      logOperation('Clearing visited profile notifications on API');

      final success = await _visitedProfileService.clearNotifications();

      if (success) {
        logOperation('Cleared notifications successfully on remote');
        return true;
      } else {
        logOperation('Failed to clear notifications on remote');
        return false;
      }
    } catch (e) {
      logOperation('Error clearing notifications on remote', e.toString());
      handleError('clearNotifications', e);
      return false;
    }
  }

  /// Delete visited profile by visited user ID on remote server
  /// Removes visited profile from server
  Future<bool> delete(String visitedUserId) async {
    try {
      logOperation('Deleting visited profile on API', visitedUserId);

      final success =
          await _visitedProfileService.deleteVisitedProfile(visitedUserId);

      if (success) {
        logOperation(
            'Deleted visited profile successfully on remote', visitedUserId);
        return true;
      } else {
        logOperation(
            'Failed to delete visited profile on remote', visitedUserId);
        return false;
      }
    } catch (e) {
      logOperation(
          'Error deleting visited profile on remote', '$visitedUserId: $e');
      handleError('delete', e);
      return false;
    }
  }

  /// Mark visited profile as read on remote server
  /// Updates the isRead flag on server
  Future<bool> markAsRead(String visitedUserId) async {
    try {
      logOperation('Marking visited profile as read on API', visitedUserId);

      final success = await _visitedProfileService.markAsRead(visitedUserId);

      if (success) {
        logOperation('Marked visited profile as read successfully on remote',
            visitedUserId);
        return true;
      } else {
        logOperation(
            'Failed to mark visited profile as read on remote', visitedUserId);
        return false;
      }
    } catch (e) {
      logOperation('Error marking visited profile as read on remote',
          '$visitedUserId: $e');
      handleError('markAsRead', e);
      return false;
    }
  }

  /// Get unread visited profiles count from remote server
  /// Returns count of unread visited profiles
  Future<int> getUnreadCount() async {
    try {
      logOperation('Getting unread visited profiles count from API');

      final count = await _visitedProfileService.getUnreadCount();
      logOperation('Fetched unread count from remote API', '$count profiles');
      return count;
    } catch (e) {
      logOperation('Error fetching unread count', e.toString());
      handleError('getUnreadCount', e);
      return 0;
    }
  }

  /// Sync visited profiles with remote server
  /// Uploads local changes and downloads remote updates
  Future<List<VisitedProfile>> syncWithRemote(
      List<VisitedProfile> localProfiles) async {
    try {
      logOperation('Syncing visited profiles with remote',
          '${localProfiles.length} local profiles');

      final syncedProfiles =
          await _visitedProfileService.syncVisitedProfiles(localProfiles);
      logOperation(
        'Synced visited profiles with remote successfully',
        '${syncedProfiles.length} profiles',
      );
      return syncedProfiles;
    } catch (e) {
      logOperation('Error syncing visited profiles with remote', e.toString());
      handleError('sync', e);
      return [];
    }
  }

  /// Batch mark multiple visited profiles as read on remote server
  /// Updates multiple isRead flags on server
  Future<bool> batchMarkAsRead(List<String> visitedUserIds) async {
    try {
      logOperation('Batch marking visited profiles as read on API',
          '${visitedUserIds.length} profiles');

      final success =
          await _visitedProfileService.batchMarkAsRead(visitedUserIds);

      if (success) {
        logOperation(
            'Batch marked visited profiles as read successfully on remote',
            '${visitedUserIds.length} profiles');
        return true;
      } else {
        logOperation('Failed to batch mark visited profiles as read on remote',
            '${visitedUserIds.length} profiles');
        return false;
      }
    } catch (e) {
      logOperation('Error batch marking visited profiles as read on remote',
          '${visitedUserIds.length} profiles: $e');
      handleError('batchMarkAsRead', e);
      return false;
    }
  }

  /// Get visited profiles by date range from remote server
  /// Returns visited profiles within specified date range
  Future<List<VisitedProfile>> getByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      logOperation('Getting visited profiles by date range from API',
          '${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      final visitedProfiles =
          await _visitedProfileService.getByDateRange(startDate, endDate);
      logOperation(
        'Fetched visited profiles by date range from remote API',
        '${visitedProfiles.length} profiles',
      );
      return visitedProfiles;
    } catch (e) {
      logOperation('Error fetching visited profiles by date range',
          '${startDate.toIso8601String()} to ${endDate.toIso8601String()}: $e');
      handleError('getByDateRange', e);
      return [];
    }
  }

  /// Get recent visited profiles from remote server
  /// Returns most recently visited profiles (limited count)
  Future<List<VisitedProfile>> getRecent({int limit = 50}) async {
    try {
      logOperation('Getting recent visited profiles from API', 'limit: $limit');

      final visitedProfiles =
          await _visitedProfileService.getRecent(limit: limit);
      logOperation(
        'Fetched recent visited profiles from remote API',
        '${visitedProfiles.length} profiles',
      );
      return visitedProfiles;
    } catch (e) {
      logOperation(
          'Error fetching recent visited profiles', 'limit: $limit: $e');
      handleError('getRecent', e);
      return [];
    }
  }
}
