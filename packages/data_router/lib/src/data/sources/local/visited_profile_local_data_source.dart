import 'package:injectable/injectable.dart';

import '../../../../../data_router.dart';
import 'base_local_data_source.dart';

/// VisitedProfile-specific local data source implementation
/// Handles VisitedProfile entity operations using ObjectBox database through VisitedProfileRepository
///
/// This follows the Single Responsibility Principle by handling only VisitedProfile entities
/// and delegates actual database operations to the existing VisitedProfileRepository.
///
/// No initialization is required as VisitedProfileRepository is injected and already
/// initialized through the dependency injection container.
@LazySingleton()
class VisitedProfileLocalDataSource
    extends BaseLocalDataSource<VisitedProfile> {
  final VisitedProfileRepository _visitedProfileRepository;

  VisitedProfileLocalDataSource(this._visitedProfileRepository);

  @override
  EntityEnum get entity => EntityEnum.visitedProfile;

  @override
  Future<VisitedProfile?> get(String id) async {
    try {
      logOperation('Getting visited profile', id);
      final visitedProfile = _visitedProfileRepository.get(id);

      if (visitedProfile != null) {
        logOperation('Found visited profile in local database', id);
        return visitedProfile;
      } else {
        logOperation('Visited profile not found in local database', id);
        return null;
      }
    } catch (e) {
      logOperation('Error getting visited profile', '$id: $e');
      throw Exception('Failed to get visited profile[$id]: $e');
    }
  }

  @override
  Future<List<VisitedProfile>> getAll() async {
    try {
      logOperation('Getting all visited profiles');
      final visitedProfiles = _visitedProfileRepository.getAll();
      logOperation(
        'Found visited profiles in local database',
        '${visitedProfiles.length} profiles',
      );
      return visitedProfiles;
    } catch (e) {
      logOperation('Error getting all visited profiles', e.toString());
      throw Exception('Failed to get all visited profiles: $e');
    }
  }

  @override
  Future<void> insert(VisitedProfile item) async {
    try {
      if (!validate(item)) {
        handleValidationError(item, 'VisitedProfile validation failed');
        return;
      }

      final id = item.visitedUserId;
      logOperation('Saving visited profile', id);

      _visitedProfileRepository.put(item);
      logOperation('Saved visited profile successfully', id);
    } catch (e) {
      logOperation('Error saving visited profile', '${item.visitedUserId}: $e');
      throw Exception(
          'Failed to save visited profile[${item.visitedUserId}]: $e');
    }
  }

  @override
  Future<void> insertAll(List<VisitedProfile> items) async {
    try {
      logOperation('Saving visited profiles', '${items.length} profiles');

      final validProfiles = <VisitedProfile>[];
      for (final item in items) {
        if (validate(item)) {
          validProfiles.add(item);
        } else {
          logOperation('Skipping invalid visited profile', item.visitedUserId);
        }
      }

      if (validProfiles.isNotEmpty) {
        _visitedProfileRepository.putMany(validProfiles);
        logOperation('Saved visited profiles successfully',
            '${validProfiles.length} profiles');
      }
    } catch (e) {
      logOperation('Error saving visited profiles', e.toString());
      throw Exception('Failed to save visited profiles: $e');
    }
  }

  @override
  Future<void> update(VisitedProfile item) async {
    try {
      if (!validate(item)) {
        handleValidationError(item, 'VisitedProfile validation failed');
        return;
      }

      final id = item.visitedUserId;
      logOperation('Updating visited profile', id);

      _visitedProfileRepository.put(item);
      logOperation('Updated visited profile successfully', id);
    } catch (e) {
      logOperation(
          'Error updating visited profile', '${item.visitedUserId}: $e');
      throw Exception(
          'Failed to update visited profile[${item.visitedUserId}]: $e');
    }
  }

  @override
  Future<void> delete(String id) async {
    try {
      logOperation('Deleting visited profile', id);
      final deleted = _visitedProfileRepository.deleteByVisitedUserId(id);

      if (deleted) {
        logOperation('Deleted visited profile successfully', id);
      } else {
        logOperation('Visited profile not found for deletion', id);
      }
    } catch (e) {
      logOperation('Error deleting visited profile', '$id: $e');
      throw Exception('Failed to delete visited profile[$id]: $e');
    }
  }

  @override
  Future<int> deleteAll() async {
    try {
      logOperation('Deleting all visited profiles');
      final deletedCount = _visitedProfileRepository.deleteAll();
      logOperation(
        'Deleted all visited profiles successfully',
        '$deletedCount profiles',
      );

      return deletedCount;
    } catch (e) {
      logOperation('Error deleting all visited profiles', e.toString());
      throw Exception('Failed to delete all visited profiles: $e');
    }
  }

  @override
  Stream<VisitedProfile?> watch(String id) {
    return _visitedProfileRepository.watch(id);
  }

  @override
  Stream<List<VisitedProfile>> watchAll() {
    logOperation('Watching all visited profiles');
    return _visitedProfileRepository.watchAll();
  }

  @override
  Future<bool> exists(String id) async {
    try {
      final exists = _visitedProfileRepository.exists(id);
      logOperation('Checking visited profile existence', '$id: $exists');
      return exists;
    } catch (e) {
      logOperation('Error checking visited profile existence', '$id: $e');
      return false;
    }
  }

  @override
  bool validate(VisitedProfile item) {
    if (item.sessionKey.isEmpty) {
      logOperation('❌ Validation failed: empty sessionKey', item.visitedUserId);
      return false;
    }

    if (item.visitedUserId.isEmpty) {
      logOperation('❌ Validation failed: empty visitedUserId', item.sessionKey);
      return false;
    }

    return true;
  }

  /// Get visited profile by visited user ID for current session
  /// Returns visited profile or null if not found
  Future<VisitedProfile?> getByVisitedUserId(String visitedUserId) async {
    try {
      logOperation('Getting visited profile by visited user ID', visitedUserId);
      return _visitedProfileRepository.getByVisitedUserId(visitedUserId);
    } catch (e) {
      logOperation('Error getting visited profile by visited user ID',
          '$visitedUserId: $e');
      return null;
    }
  }

  /// Get all visited profiles for current session
  /// Returns list ordered by update time (most recent first)
  Future<List<VisitedProfile>> getAllForCurrentSession() async {
    try {
      logOperation('Getting all visited profiles for current session');
      return _visitedProfileRepository.getAllForCurrentSession();
    } catch (e) {
      logOperation(
          'Error getting visited profiles for current session', e.toString());
      return [];
    }
  }

  /// Watch visited profiles for current session
  /// Returns stream of visited profiles updates
  Stream<List<VisitedProfile>> watchVisitedProfiles() {
    logOperation('Watching visited profiles for current session');
    return _visitedProfileRepository.watchVisitedProfiles();
  }

  /// Delete visited profile by visited user ID
  /// Removes visited profile for current session
  Future<void> deleteByVisitedUserId(String visitedUserId) async {
    try {
      logOperation(
          'Deleting visited profile by visited user ID', visitedUserId);
      _visitedProfileRepository.deleteByVisitedUserId(visitedUserId);
      logOperation('Deleted visited profile by visited user ID successfully',
          visitedUserId);
    } catch (e) {
      logOperation('Error deleting visited profile by visited user ID',
          '$visitedUserId: $e');
      throw Exception(
          'Failed to delete visited profile by visited user ID[$visitedUserId]: $e');
    }
  }

  /// Mark all visited profiles as read for current session
  /// Returns count of updated profiles
  Future<int> markAllAsRead() async {
    try {
      logOperation('Marking all visited profiles as read');
      final count = _visitedProfileRepository.markAllAsRead();
      logOperation('Marked all visited profiles as read successfully',
          '$count profiles');
      return count;
    } catch (e) {
      logOperation('Error marking all visited profiles as read', e.toString());
      throw Exception('Failed to mark all visited profiles as read: $e');
    }
  }

  /// Delete all visited profiles by session key
  /// This is used for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting all visited profiles by session key', sessionKey);
      _visitedProfileRepository.deleteAllBySessionKey(sessionKey);
      logOperation('Deleted all visited profiles by session key successfully',
          sessionKey);
    } catch (e) {
      logOperation(
          'Error deleting visited profiles by session key', '$sessionKey: $e');
      throw Exception(
          'Failed to delete visited profiles by session key[$sessionKey]: $e');
    }
  }
}
