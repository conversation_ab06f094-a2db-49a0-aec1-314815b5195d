import 'dart:async';
import 'dart:convert';

import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../../../../../data_router.dart';
import '../../../core/websocket/data_router_websocket_manager.dart';
import 'base_websocket_data_source.dart';
import 'events/data_router_cloud_event.dart';

/// VisitedProfile-specific WebSocket data source implementation
/// Simplified architecture: filters VisitedProfile events and broadcasts to DataOperation
///
/// New Responsibilities:
/// - Filter WebSocket events relevant to VisitedProfile entities
/// - Parse and validate VisitedProfile events
/// - Broadcast filtered events to VisitedProfileDataOperation via callback
/// - No longer manages individual subscriptions (handled by LocalDataSource watch)
@LazySingleton()
class VisitedProfileWebSocketDataSource extends BaseWebSocketDataSource<VisitedProfile> {
  final DataRouterWebSocketManager _webSocketManager;

  late final StreamSubscription _eventSubscription;
  bool _disposed = false;

  /// Callback to send filtered CloudEvent events to DataOperation
  void Function(CloudEvent event)? _dataOperationCallback;

  VisitedProfileWebSocketDataSource()
      : _webSocketManager = GetIt.instance.get<WebSocketProvider>()
            as DataRouterWebSocketManager {
    _eventSubscription = _webSocketManager.rawMessageStream
        .where(_shouldHandleMessage)
        .listen(_handleVisitedProfileMessage);

    logOperation('VisitedProfileWebSocketDataSource initialized - filtering VisitedProfile events');
  }

  @override
  String get entityName => 'VisitedProfile';

  @override
  void setDataOperationCallback(void Function(CloudEvent event) callback) {
    _dataOperationCallback = callback;
    logOperation('DataOperation callback set for VisitedProfile events');
  }

  @override
  bool get isConnected => _webSocketManager.isConnected;

  @override
  Stream<bool> get connectionStatus => _webSocketManager.connectionStatusStream;

  /// Check if this message should be handled by VisitedProfile data source
  bool _shouldHandleMessage(dynamic message) {
    if (message is! String) return false;

    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      final dataRouterEvent = DataRouterCloudEvent.fromJsonMap(data);

      // Check if this is a VisitedProfile-related CloudEvent
      return _isVisitedProfileEvent(dataRouterEvent.type);
    } catch (e) {
      return false;
    }
  }

  /// Check if event type is related to VisitedProfile
  bool _isVisitedProfileEvent(String eventType) {
    const visitedProfileEvents = {
      'VISITED_PROFILE_CREATED',
      'VISITED_PROFILE_UPDATED',
      'VISITED_PROFILE_DELETED',
      'VISITED_PROFILE_CLEARED',
      'VISITED_PROFILE_MARKED_READ',
      'VISITED_PROFILE_MARKED_UNREAD',
      'VISITED_PROFILE_BATCH_UPDATED',
    };

    return visitedProfileEvents.contains(eventType);
  }

  /// Handle VisitedProfile-related WebSocket messages
  void _handleVisitedProfileMessage(dynamic message) {
    if (_disposed) return;

    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final cloudEvent = DataRouterCloudEvent.fromJsonMap(data);

      logOperation(
        'Received VisitedProfile WebSocket event',
        '${cloudEvent.type} - ${cloudEvent.source}',
      );

      // Forward to DataOperation for processing
      _dataOperationCallback?.call(cloudEvent);
    } catch (e) {
      logOperation(
        'Error processing VisitedProfile WebSocket message',
        e.toString(),
      );
    }
  }

  /// Send visited profile event through WebSocket
  /// This method allows sending visited profile-related events to the server
  Future<void> sendVisitedProfileEvent({
    required String eventType,
    required Map<String, dynamic> data,
    String? visitedUserId,
  }) async {
    try {
      if (!isConnected) {
        logOperation('❌ Cannot send event - WebSocket not connected', eventType);
        return;
      }

      final cloudEvent = DataRouterCloudEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        source: 'mobile-client',
        type: eventType,
        data: data,
        time: DateTime.now(),
        specVersion: '1.0',
      );

      final message = jsonEncode(cloudEvent.toJsonMap());
      await _webSocketManager.sendMessage(message);

      logOperation(
        '✅ Sent VisitedProfile WebSocket event',
        '$eventType${visitedUserId != null ? ' for $visitedUserId' : ''}',
      );
    } catch (e) {
      logOperation(
        '❌ Error sending VisitedProfile WebSocket event',
        '$eventType: $e',
      );
    }
  }

  /// Send visited profile created event
  Future<void> sendVisitedProfileCreated(VisitedProfile visitedProfile) async {
    await sendVisitedProfileEvent(
      eventType: 'VISITED_PROFILE_CREATED',
      data: {
        'sessionKey': visitedProfile.sessionKey,
        'visitedUserId': visitedProfile.visitedUserId,
        'isRead': visitedProfile.isRead,
        'createTime': visitedProfile.createTime?.toIso8601String(),
        'updateTime': visitedProfile.updateTime?.toIso8601String(),
      },
      visitedUserId: visitedProfile.visitedUserId,
    );
  }

  /// Send visited profile updated event
  Future<void> sendVisitedProfileUpdated(VisitedProfile visitedProfile) async {
    await sendVisitedProfileEvent(
      eventType: 'VISITED_PROFILE_UPDATED',
      data: {
        'sessionKey': visitedProfile.sessionKey,
        'visitedUserId': visitedProfile.visitedUserId,
        'isRead': visitedProfile.isRead,
        'updateTime': visitedProfile.updateTime?.toIso8601String(),
      },
      visitedUserId: visitedProfile.visitedUserId,
    );
  }

  /// Send visited profile deleted event
  Future<void> sendVisitedProfileDeleted(String visitedUserId) async {
    await sendVisitedProfileEvent(
      eventType: 'VISITED_PROFILE_DELETED',
      data: {
        'visitedUserId': visitedUserId,
      },
      visitedUserId: visitedUserId,
    );
  }

  /// Send visited profile marked as read event
  Future<void> sendVisitedProfileMarkedRead(String visitedUserId) async {
    await sendVisitedProfileEvent(
      eventType: 'VISITED_PROFILE_MARKED_READ',
      data: {
        'visitedUserId': visitedUserId,
        'isRead': true,
        'updateTime': DateTime.now().toIso8601String(),
      },
      visitedUserId: visitedUserId,
    );
  }

  /// Send visited profile cleared event (all marked as read)
  Future<void> sendVisitedProfileCleared() async {
    await sendVisitedProfileEvent(
      eventType: 'VISITED_PROFILE_CLEARED',
      data: {
        'clearedAt': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Send batch visited profiles marked as read event
  Future<void> sendBatchVisitedProfilesMarkedRead(List<String> visitedUserIds) async {
    await sendVisitedProfileEvent(
      eventType: 'VISITED_PROFILE_BATCH_UPDATED',
      data: {
        'visitedUserIds': visitedUserIds,
        'isRead': true,
        'updateTime': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Subscribe to visited profile events for specific user
  /// This creates a targeted subscription for a specific visited user
  void subscribeToVisitedProfileEvents(String visitedUserId) {
    logOperation('Subscribing to visited profile events', visitedUserId);
    
    // Send subscription request through WebSocket
    sendVisitedProfileEvent(
      eventType: 'SUBSCRIBE_VISITED_PROFILE',
      data: {
        'visitedUserId': visitedUserId,
        'subscribeAt': DateTime.now().toIso8601String(),
      },
      visitedUserId: visitedUserId,
    );
  }

  /// Unsubscribe from visited profile events for specific user
  void unsubscribeFromVisitedProfileEvents(String visitedUserId) {
    logOperation('Unsubscribing from visited profile events', visitedUserId);
    
    // Send unsubscription request through WebSocket
    sendVisitedProfileEvent(
      eventType: 'UNSUBSCRIBE_VISITED_PROFILE',
      data: {
        'visitedUserId': visitedUserId,
        'unsubscribeAt': DateTime.now().toIso8601String(),
      },
      visitedUserId: visitedUserId,
    );
  }

  /// Subscribe to all visited profile events for current session
  void subscribeToAllVisitedProfileEvents() {
    logOperation('Subscribing to all visited profile events');
    
    sendVisitedProfileEvent(
      eventType: 'SUBSCRIBE_ALL_VISITED_PROFILES',
      data: {
        'subscribeAt': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Dispose resources and clean up subscriptions
  @override
  void dispose() {
    if (_disposed) return;
    
    logOperation('Disposing VisitedProfileWebSocketDataSource');
    _disposed = true;
    _eventSubscription.cancel();
    _dataOperationCallback = null;
  }
}
