import '../../../data_router.dart';

/// VisitedProfile service interface
/// Defines the contract for VisitedProfile-related API operations
///
/// This interface abstracts the API layer for VisitedProfile entities,
/// allowing different implementations (REST API, GraphQL, etc.) while
/// maintaining a consistent interface for the data layer.
///
/// All methods should handle network errors gracefully and return
/// appropriate results or throw meaningful exceptions.
abstract class VisitedProfileService {
  /// Get visited profile by visited user ID
  /// Returns visited profile or null if not found
  /// 
  /// [visitedUserId] - The ID of the visited user
  /// 
  /// Throws [Exception] if network error occurs
  Future<VisitedProfile?> getVisitedProfile(String visitedUserId);

  /// Get all visited profiles for current user
  /// Returns list of visited profiles ordered by update time
  /// 
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> getVisitedProfiles();

  /// Record a visit to a user profile
  /// Creates or updates visited profile entry
  /// 
  /// [visitedUserId] - The ID of the visited user
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> recordVisit(String visitedUserId);

  /// Clear all visited profile notifications
  /// Marks all visited profiles as read on server
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> clearNotifications();

  /// Delete visited profile by visited user ID
  /// Removes visited profile from server
  /// 
  /// [visitedUserId] - The ID of the visited user
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> deleteVisitedProfile(String visitedUserId);

  /// Mark visited profile as read
  /// Updates the isRead flag on server
  /// 
  /// [visitedUserId] - The ID of the visited user
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> markAsRead(String visitedUserId);

  /// Mark visited profile as unread
  /// Updates the isRead flag on server
  /// 
  /// [visitedUserId] - The ID of the visited user
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> markAsUnread(String visitedUserId);

  /// Get unread visited profiles count
  /// Returns count of unread visited profiles
  /// 
  /// Throws [Exception] if network error occurs
  Future<int> getUnreadCount();

  /// Sync visited profiles with server
  /// Uploads local changes and downloads remote updates
  /// 
  /// [localProfiles] - List of local visited profiles to sync
  /// 
  /// Returns updated list of visited profiles from server
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> syncVisitedProfiles(List<VisitedProfile> localProfiles);

  /// Batch mark multiple visited profiles as read
  /// Updates multiple isRead flags on server
  /// 
  /// [visitedUserIds] - List of visited user IDs to mark as read
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> batchMarkAsRead(List<String> visitedUserIds);

  /// Batch mark multiple visited profiles as unread
  /// Updates multiple isRead flags on server
  /// 
  /// [visitedUserIds] - List of visited user IDs to mark as unread
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> batchMarkAsUnread(List<String> visitedUserIds);

  /// Get visited profiles by date range
  /// Returns visited profiles within specified date range
  /// 
  /// [startDate] - Start date of the range
  /// [endDate] - End date of the range
  /// 
  /// Returns list of visited profiles within date range
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> getByDateRange(DateTime startDate, DateTime endDate);

  /// Get recent visited profiles
  /// Returns most recently visited profiles (limited count)
  /// 
  /// [limit] - Maximum number of profiles to return (default: 50)
  /// 
  /// Returns list of recent visited profiles
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> getRecent({int limit = 50});

  /// Get visited profiles with pagination
  /// Returns paginated list of visited profiles
  /// 
  /// [offset] - Number of profiles to skip
  /// [limit] - Maximum number of profiles to return
  /// 
  /// Returns list of visited profiles for the specified page
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> getPaginated({int offset = 0, int limit = 50});

  /// Search visited profiles by user name or display name
  /// Returns visited profiles matching the search query
  /// 
  /// [query] - Search query string
  /// [limit] - Maximum number of profiles to return (default: 50)
  /// 
  /// Returns list of visited profiles matching the search
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> searchVisitedProfiles(String query, {int limit = 50});

  /// Get visited profiles statistics
  /// Returns statistics about visited profiles (total count, unread count, etc.)
  /// 
  /// Returns map containing statistics
  /// Throws [Exception] if network error occurs
  Future<Map<String, dynamic>> getStatistics();

  /// Export visited profiles data
  /// Returns visited profiles data in specified format
  /// 
  /// [format] - Export format ('json', 'csv', etc.)
  /// 
  /// Returns exported data as string
  /// Throws [Exception] if network error occurs
  Future<String> exportData({String format = 'json'});

  /// Import visited profiles data
  /// Imports visited profiles from provided data
  /// 
  /// [data] - Data to import
  /// [format] - Data format ('json', 'csv', etc.)
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> importData(String data, {String format = 'json'});

  /// Bulk delete visited profiles
  /// Deletes multiple visited profiles by their visited user IDs
  /// 
  /// [visitedUserIds] - List of visited user IDs to delete
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> bulkDelete(List<String> visitedUserIds);

  /// Get visited profiles by user IDs
  /// Returns visited profiles for specific user IDs
  /// 
  /// [visitedUserIds] - List of visited user IDs to fetch
  /// 
  /// Returns list of visited profiles for the specified user IDs
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> getByUserIds(List<String> visitedUserIds);

  /// Update visited profile metadata
  /// Updates additional metadata for visited profile
  /// 
  /// [visitedUserId] - The ID of the visited user
  /// [metadata] - Additional metadata to update
  /// 
  /// Returns true if successful, false otherwise
  /// Throws [Exception] if network error occurs
  Future<bool> updateMetadata(String visitedUserId, Map<String, dynamic> metadata);

  /// Get visited profiles with filters
  /// Returns filtered list of visited profiles
  /// 
  /// [filters] - Map of filter criteria
  /// 
  /// Returns list of visited profiles matching the filters
  /// Throws [Exception] if network error occurs
  Future<List<VisitedProfile>> getWithFilters(Map<String, dynamic> filters);
}
