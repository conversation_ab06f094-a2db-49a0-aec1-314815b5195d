import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:objectbox/objectbox.dart';

import '../../../data_router.dart';
import '../../data/local/session_box.dart';
import 'base_repository.dart';

/// VisitedProfile repository implementation following BaseRepository pattern
/// Provides session-based data access with automatic filtering for VisitedProfile entities
///
/// This repository extends BaseRepository to inherit session management capabilities
/// and implements VisitedProfile-specific operations using ObjectBox native watch functionality.
///
/// All operations automatically filter by the current active session key,
/// ensuring data isolation between different user sessions.
@LazySingleton()
class VisitedProfileRepository extends BaseRepository<VisitedProfile> {
  final SessionBox<VisitedProfile> _sessionBox;

  VisitedProfileRepository(this._sessionBox) : super(_sessionBox);

  /// Get visited profile by visited user ID for current session
  /// Returns visited profile or null if not found
  VisitedProfile? getByVisitedUserId(String visitedUserId) {
    try {
      final query = queryBuilder()
          .and(VisitedProfile_.visitedUserIdField.equals(visitedUserId))
          .build();
      
      final result = query.findFirst();
      query.close();
      
      return result;
    } catch (e) {
      throw Exception('Failed to get visited profile by visited user ID[$visitedUserId]: $e');
    }
  }

  /// Get all visited profiles for current session ordered by update time
  /// Returns list of visited profiles (most recent first)
  List<VisitedProfile> getAllForCurrentSession() {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: Order.descending)
          .build();
      
      final results = query.find();
      query.close();
      
      return results;
    } catch (e) {
      throw Exception('Failed to get all visited profiles for current session: $e');
    }
  }

  /// Watch visited profiles for current session using ObjectBox native watch
  /// Returns stream of visited profiles updates (most recent first)
  Stream<List<VisitedProfile>> watchVisitedProfiles() {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: Order.descending)
          .watch(triggerImmediately: true);
      
      return query.map((query) => query.find());
    } catch (e) {
      throw Exception('Failed to watch visited profiles: $e');
    }
  }

  /// Watch specific visited profile by visited user ID using ObjectBox native watch
  /// Returns stream of visited profile updates
  Stream<VisitedProfile?> watch(String visitedUserId) {
    try {
      final query = queryBuilder()
          .and(VisitedProfile_.visitedUserIdField.equals(visitedUserId))
          .watch(triggerImmediately: true);
      
      return query.map((query) => query.findFirst());
    } catch (e) {
      throw Exception('Failed to watch visited profile[$visitedUserId]: $e');
    }
  }

  /// Watch all visited profiles using ObjectBox native watch
  /// Returns stream of all visited profiles updates
  Stream<List<VisitedProfile>> watchAll() {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: Order.descending)
          .watch(triggerImmediately: true);
      
      return query.map((query) => query.find());
    } catch (e) {
      throw Exception('Failed to watch all visited profiles: $e');
    }
  }

  /// Delete visited profile by visited user ID for current session
  /// Returns true if deleted, false if not found
  bool deleteByVisitedUserId(String visitedUserId) {
    try {
      final query = queryBuilder()
          .and(VisitedProfile_.visitedUserIdField.equals(visitedUserId))
          .build();
      
      final deletedCount = query.remove();
      query.close();
      
      return deletedCount > 0;
    } catch (e) {
      throw Exception('Failed to delete visited profile by visited user ID[$visitedUserId]: $e');
    }
  }

  /// Mark all visited profiles as read for current session
  /// Returns count of updated profiles
  int markAllAsRead() {
    try {
      final profiles = getAllForCurrentSession();
      int updatedCount = 0;
      
      for (final profile in profiles) {
        if (!profile.isRead) {
          profile.markAsRead();
          put(profile);
          updatedCount++;
        }
      }
      
      return updatedCount;
    } catch (e) {
      throw Exception('Failed to mark all visited profiles as read: $e');
    }
  }

  /// Mark visited profile as read by visited user ID
  /// Returns true if updated, false if not found
  bool markAsReadByVisitedUserId(String visitedUserId) {
    try {
      final profile = getByVisitedUserId(visitedUserId);
      if (profile == null) {
        return false;
      }
      
      if (!profile.isRead) {
        profile.markAsRead();
        put(profile);
      }
      
      return true;
    } catch (e) {
      throw Exception('Failed to mark visited profile as read[$visitedUserId]: $e');
    }
  }

  /// Mark visited profile as unread by visited user ID
  /// Returns true if updated, false if not found
  bool markAsUnreadByVisitedUserId(String visitedUserId) {
    try {
      final profile = getByVisitedUserId(visitedUserId);
      if (profile == null) {
        return false;
      }
      
      if (profile.isRead) {
        profile.markAsUnread();
        put(profile);
      }
      
      return true;
    } catch (e) {
      throw Exception('Failed to mark visited profile as unread[$visitedUserId]: $e');
    }
  }

  /// Get unread visited profiles count for current session
  /// Returns count of unread visited profiles
  int getUnreadCount() {
    try {
      final query = queryBuilder()
          .and(VisitedProfile_.isRead.equals(false))
          .build();
      
      final count = query.count();
      query.close();
      
      return count;
    } catch (e) {
      throw Exception('Failed to get unread visited profiles count: $e');
    }
  }

  /// Get recent visited profiles for current session
  /// Returns list of recent visited profiles (limited count)
  List<VisitedProfile> getRecent({int limit = 50}) {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: Order.descending)
          .build();
      
      final results = query.find(offset: 0, limit: limit);
      query.close();
      
      return results;
    } catch (e) {
      throw Exception('Failed to get recent visited profiles: $e');
    }
  }

  /// Get visited profiles by date range for current session
  /// Returns visited profiles within specified date range
  List<VisitedProfile> getByDateRange(DateTime startDate, DateTime endDate) {
    try {
      final query = queryBuilder()
          .and(VisitedProfile_.updateTime.between(
            startDate.millisecondsSinceEpoch,
            endDate.millisecondsSinceEpoch,
          ))
          .order(VisitedProfile_.updateTime, flags: Order.descending)
          .build();
      
      final results = query.find();
      query.close();
      
      return results;
    } catch (e) {
      throw Exception('Failed to get visited profiles by date range: $e');
    }
  }

  /// Check if visited profile exists by visited user ID for current session
  /// Returns true if exists, false otherwise
  bool exists(String visitedUserId) {
    try {
      final query = queryBuilder()
          .and(VisitedProfile_.visitedUserIdField.equals(visitedUserId))
          .build();
      
      final exists = query.count() > 0;
      query.close();
      
      return exists;
    } catch (e) {
      throw Exception('Failed to check visited profile existence[$visitedUserId]: $e');
    }
  }

  /// Delete all visited profiles by session key
  /// This is used for session cleanup - can delete across sessions
  void deleteAllBySessionKey(String sessionKey) {
    try {
      final query = _sessionBox.box
          .query(VisitedProfile_.sessionKey.equals(sessionKey))
          .build();
      
      query.remove();
      query.close();
    } catch (e) {
      throw Exception('Failed to delete visited profiles by session key[$sessionKey]: $e');
    }
  }

  /// Delete all visited profiles for current session
  /// Returns count of deleted profiles
  int deleteAll() {
    try {
      final query = queryBuilder().build();
      final deletedCount = query.remove();
      query.close();
      
      return deletedCount;
    } catch (e) {
      throw Exception('Failed to delete all visited profiles: $e');
    }
  }

  /// Update visit time for visited profile
  /// Creates new profile if doesn't exist, updates existing if found
  VisitedProfile updateVisitTime(String visitedUserId) {
    try {
      final existing = getByVisitedUserId(visitedUserId);
      
      if (existing != null) {
        existing.updateVisitTime();
        existing.markAsUnread(); // New visit should be unread
        put(existing);
        return existing;
      } else {
        // Create new visited profile
        final newProfile = VisitedProfile.create(
          sessionKey: _sessionBox.activeSessionKey,
          visitedUserId: visitedUserId,
          isRead: false,
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
        );
        put(newProfile);
        return newProfile;
      }
    } catch (e) {
      throw Exception('Failed to update visit time[$visitedUserId]: $e');
    }
  }
}
