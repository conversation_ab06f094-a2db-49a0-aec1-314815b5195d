/// Local-first data router with Clean Architecture
///
/// {@template data_router}
/// Local-first data router with Clean Architecture and Unified API Client
/// {@endtemplate}
library data_router;

// Core configuration
export 'src/core/config/config.dart';

// Core DataRouter class
export 'src/core/data_router.dart';

// Core infrastructure
export 'src/core/database.dart';
export 'src/core/di/di.dart';

// Core logging
export 'src/core/logging/data_router_logger.dart';

// Core enums
export 'src/core/enums/enums.dart';

// Core interfaces
export 'src/core/interfaces/data_operation.dart';
export 'src/core/interfaces/model_adapter.dart';

// Data layer - Adapters
export 'src/data/adapters/user_adapter.dart';
export 'src/data/adapters/channel_adapter.dart';

// Data layer - Database entities
// Session entities
export 'src/data/database/entities/session.dart';
export 'src/data/database/entities/session_local_metadata.dart';

// User entities
export 'src/data/database/entities/user.dart';
export 'src/data/database/entities/profile.dart';
export 'src/data/database/entities/visited_profile.dart';
export 'src/data/database/entities/user_presence.dart';
export 'src/data/database/entities/user_status.dart';

// Channel entities
export 'src/data/database/entities/channel.dart';
export 'src/data/database/entities/member.dart';

// Message entities
export 'src/data/database/entities/message.dart';
export 'src/data/database/entities/attachment.dart';

// Friend entities
export 'src/data/database/entities/friend.dart';

// Manager entities
export 'src/data/database/entities/manager.dart';

// Translation entities
export 'src/data/database/entities/translated_result.dart';

// Private data entities
export 'src/data/database/entities/private_data.dart';
export 'src/data/database/entities/user_private_data.dart';
export 'src/data/database/entities/channel_private_data.dart';

// Call entities
export 'src/data/database/entities/call_log.dart';
export 'src/data/database/entities/call_log_private_data.dart';

// History entities
export 'src/data/database/entities/search_history.dart';

// Sticker entities
export 'src/data/database/entities/collection.dart';
export 'src/data/database/entities/sticker.dart';
export 'src/data/database/entities/sticker_frame_count.dart';

// Data layer - Helpers
export 'src/data/helpers/user_helper.dart';

// Data layer - Local storage
export 'src/data/local/session_box.dart';

// Data layer - Models
export 'src/data/models/auth_models.dart';
export 'src/data/models/paginated_channels_result.dart';
export 'src/data/models/resource.dart';
export 'src/data/models/security_key_models.dart';

// Data layer - Operations
export 'src/data/operations/call_data_operation.dart';
export 'src/data/operations/session_data_operation.dart';
export 'src/data/operations/user_data_operation.dart';
export 'src/data/operations/visited_profile_data_operation.dart';
export 'src/data/operations/channel_data_operation.dart';
export 'src/data/operations/member_data_operation.dart';
export 'src/data/operations/friend_data_operation.dart';
export 'src/data/operations/message_data_operation.dart';
export 'src/data/operations/sticker_data_operation.dart';
export 'src/data/operations/private_data_data_operation.dart';

// WebSocket layer
export 'src/core/websocket/websocket_provider.dart';
export 'src/core/websocket/websocket_config.dart';
export 'src/data/models/websocket_connection_status.dart';

// WebSocket Events
export 'src/data/sources/ws/events/events.dart';

// WebSocket Data Sources
export 'src/data/sources/ws/friend_websocket_data_source.dart';

// Data layer - Serializers
export 'src/data/serializers/user_serializer.dart';
export 'src/data/serializers/visited_profile_serializer.dart';
export 'src/data/serializers/channel_serializer.dart';
export 'src/data/serializers/member_serializer.dart';
export 'src/data/serializers/friend_serializer.dart';
export 'src/data/serializers/message_serializer.dart';
export 'src/data/serializers/attachment_serializer.dart';
export 'src/data/serializers/collection_serializer.dart';
export 'src/data/serializers/sticker_serializer.dart';
export 'src/data/serializers/translated_result_serializer.dart';

// Data layer - Services
export 'src/data/services/interfaces/message_service.dart';
export 'src/data/services/sticker_service.dart';
export 'src/data/services/sticker_service_impl.dart';
export 'src/domain/services/channel_service.dart';
export 'src/domain/services/member_service.dart';
export 'src/domain/services/friend_service.dart';
export 'src/domain/services/visited_profile_service.dart';

// Data layer - Data Sources
export 'src/data/sources/local/call_local_data_source.dart';
export 'src/data/sources/remote/call_remote_data_source.dart';
export 'src/data/sources/ws/call_websocket_data_source.dart';
export 'src/data/sources/local/visited_profile_local_data_source.dart';
export 'src/data/sources/remote/visited_profile_remote_data_source.dart';
export 'src/data/sources/ws/visited_profile_websocket_data_source.dart';
export 'src/data/sources/local/sticker_local_data_source.dart';
export 'src/data/sources/remote/sticker_remote_data_source.dart';
export 'src/data/sources/ws/sticker_websocket_data_source.dart';
export 'src/data/sources/local/private_data_local_data_source.dart';
export 'src/data/sources/remote/private_data_remote_data_source.dart';
export 'src/data/sources/ws/private_data_websocket_data_source.dart';

// Data layer - Repositories
export 'src/domain/repositories/base_repository.dart';
export 'src/domain/repositories/call_repository.dart';
export 'src/data/repositories/call_repository_impl.dart';
export 'src/domain/repositories/visited_profile_repository.dart';
export 'src/domain/repositories/channel_repository.dart';
export 'src/domain/repositories/member_repository.dart';
export 'src/domain/repositories/message_repository.dart';
export 'src/domain/repositories/friend_repository.dart';
export 'src/domain/repositories/sticker_repository.dart';
export 'src/data/repositories/sticker_repository_impl.dart';
export 'src/domain/repositories/private_data_repository.dart';
export 'src/data/repositories/private_data_repository_impl.dart';

// Legacy API layer (will be deprecated)
export 'src/legacy/api/api.dart';

// Shared types
export 'src/shared/types/result.dart';
